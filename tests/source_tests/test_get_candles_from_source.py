"""
Test suite for get_candles_from_{source} functions.

This test file discovers and runs tests for all get_candles_from_{source}.py modules
in the app/sources/{source}/ directory automatically.
"""

import os
import sys
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Tuple, Callable

from app.core.discover_source_functions import discover_source_functions

# Add the project root to the Python path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# ============================================================================
# TEST CONFIGURATION - EASILY MODIFY THESE PARAMETERS
# ============================================================================

# Test parameters - modify these to change test behavior
TEST_CONFIG = {
        'currency_pair': 'btc-usd',  # Trading pair to test
        'timeframe'    : 'h1',  # Timeframe: m1, m5, m15, m30, h1, h4, d1
        'candles'      : 5,  # Number of candles to request
        'ecc'          : True  # Exclude current candle
}

# Timeframe-specific validation rules
TIMEFRAME_RULES = {
        'm1' : {
                'max_age_minutes' : 2,  # Data should not be older than 2 minutes
                'interval_minutes': 1  # 1 minute intervals
        },
        'm5' : {
                'max_age_minutes' : 10,  # Data should not be older than 10 minutes
                'interval_minutes': 5  # 5 minute intervals
        },
        'm15': {
                'max_age_minutes' : 30,  # Data should not be older than 30 minutes
                'interval_minutes': 15  # 15 minute intervals
        },
        'm30': {
                'max_age_minutes' : 60,  # Data should not be older than 1 hour
                'interval_minutes': 30  # 30 minute intervals
        },
        'h1' : {
                'max_age_minutes' : 120,  # Data should not be older than 2 hours
                'interval_minutes': 60  # 1 hour intervals
        },
        'h4' : {
                'max_age_minutes' : 480,  # Data should not be older than 8 hours
                'interval_minutes': 240  # 4 hour intervals
        },
        'd1' : {
                'max_age_minutes' : 1440,  # Data should not be older than 24 hours
                'interval_minutes': 1440  # 1 day intervals
        }
}


# ============================================================================


def get_dataframe_sample(df: pd.DataFrame, n: int = None) -> dict:
    """
    Get sample data from DataFrame showing timestamps in descending order from newest.

    Args:
        df: DataFrame to sample
        n: Number of rows to show (defaults to TEST_CONFIG['candles'])

    Returns:
        Dictionary with timestamp sample data
    """
    if df is None or df.empty:
        return {"timestamps_sample": "No data", "total_rows": 0, "columns": []}

    if n is None:
        n = TEST_CONFIG['candles']

    try:
        # Sort by timestamp descending (newest first) and take n rows
        if 'timestamp' in df.columns:
            df_sorted = df.sort_values('timestamp', ascending = False)
            sample_df = df_sorted.head(n)

            # Convert timestamps to readable format for display
            display_df = sample_df.copy()
            if 'timestamp' in display_df.columns:
                display_df['datetime'] = pd.to_datetime(display_df['timestamp'], unit = 's')
                # Reorder columns to show datetime first, then timestamp, then others
                cols = ['datetime', 'timestamp'] + [col for col in display_df.columns if
                                                    col not in ['datetime', 'timestamp']]
                display_df = display_df[cols]

            # Convert to string representation with better formatting
            sample_str = display_df.to_string(
                    index = False,
                    max_cols = 10,
                    float_format = lambda x: f'{x:.4f}' if pd.notnull(x) else 'NaN'
            )
        else:
            # If no timestamp column, just show first n rows
            sample_df = df.head(n)
            sample_str = sample_df.to_string(
                    index = False,
                    max_cols = 10,
                    float_format = lambda x: f'{x:.4f}' if pd.notnull(x) else 'NaN'
            )

        return {
                "timestamps_sample": sample_str,
                "total_rows"       : len(df),
                "columns"          : list(df.columns),
                "sample_rows"      : len(sample_df) if 'sample_df' in locals() else 0
        }
    except Exception as e:
        return {
                "timestamps_sample": f"Error sampling data: {e}",
                "total_rows"       : len(df) if df is not None else 0,
                "columns"          : list(df.columns) if df is not None else [],
                "sample_rows"      : 0
        }


def validate_dataframe_response(df: pd.DataFrame, source_name: str, timeframe: str, expected_candles: int) -> Tuple[
    bool, List[str], dict]:
    """
    Validate that the DataFrame response meets the requirements based on timeframe.

    Args:
        df: DataFrame to validate
        source_name: Name of the source for error reporting
        timeframe: Timeframe being tested (e.g., 'm1', 'h1', 'd1')
        expected_candles: Expected number of candles

    Returns:
        Tuple of (is_valid, list_of_errors, sample_data)
    """
    errors = []
    sample_data = get_dataframe_sample(df, expected_candles)

    # Get timeframe-specific rules
    if timeframe not in TIMEFRAME_RULES:
        errors.append(f"{source_name}: Unknown timeframe '{timeframe}'. Supported: {list(TIMEFRAME_RULES.keys())}")
        return False, errors, sample_data

    rules = TIMEFRAME_RULES[timeframe]
    max_age_minutes = rules['max_age_minutes']
    interval_minutes = rules['interval_minutes']

    # Check if DataFrame is None or empty
    if df is None:
        errors.append(f"{source_name}: Function returned None")
        return False, errors, sample_data

    if df.empty:
        errors.append(f"{source_name}: Function returned empty DataFrame")
        return False, errors, sample_data

    # Check if DataFrame has the expected number of rows (at least expected_candles)
    if len(df) < expected_candles:
        errors.append(f"{source_name}: Expected at least {expected_candles} rows, got {len(df)}")

    # Check if DataFrame has timestamp column
    if 'timestamp' not in df.columns:
        errors.append(f"{source_name}: DataFrame missing 'timestamp' column. Available columns: {list(df.columns)}")
        return False, errors, sample_data

    # Check if timestamps are valid
    try:
        # Convert timestamps to datetime if they're not already
        if df['timestamp'].dtype == 'object':
            # Try to convert string timestamps to numeric
            timestamps = pd.to_numeric(df['timestamp'], errors = 'coerce')
        else:
            timestamps = df['timestamp']

        # Check for any NaN timestamps
        if timestamps.isna().any():
            errors.append(f"{source_name}: DataFrame contains invalid timestamps")
            return False, errors, sample_data

        # Get the newest timestamp
        newest_timestamp = timestamps.max()
        current_time = datetime.now().timestamp()

        # Check if newest timestamp is not older than the timeframe-specific limit
        time_diff_minutes = (current_time - newest_timestamp) / 60
        if time_diff_minutes > max_age_minutes:
            if max_age_minutes >= 60:
                max_age_hours = max_age_minutes / 60
                time_diff_hours = time_diff_minutes / 60
                errors.append(
                        f"{source_name}: Newest timestamp is {time_diff_hours:.1f} hours old (should be ≤ {max_age_hours:.1f} hours for {timeframe})")
            else:
                errors.append(
                        f"{source_name}: Newest timestamp is {time_diff_minutes:.1f} minutes old (should be ≤ {max_age_minutes} minutes for {timeframe})")

        # Check if we have at least the expected number of different timestamps
        unique_timestamps = timestamps.nunique()
        if unique_timestamps < expected_candles:
            errors.append(
                    f"{source_name}: Expected at least {expected_candles} unique timestamps, got {unique_timestamps}")

    except Exception as e:
        errors.append(f"{source_name}: Error validating timestamps: {str(e)}")
        return False, errors, sample_data

    # Check for required OHLC columns
    required_columns = ['open', 'high', 'low', 'close']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        errors.append(f"{source_name}: Missing required columns: {missing_columns}")

    return len(errors) == 0, errors, sample_data


class TestResult:
    """Simple test result container."""

    def __init__(self, test_name: str, source_name: str, passed: bool, message: str = "", details: dict = None,
                 sample_data: dict = None):
        self.test_name = test_name
        self.source_name = source_name
        self.passed = passed
        self.message = message
        self.details = details or {}
        self.sample_data = sample_data or {}


class SourceTestRunner:
    """Simple test runner for get_candles_from_{source} functions."""

    def __init__(self):
        self.results = []
        self.discovered_sources = []

    def discover_sources(self) -> bool:
        """Discover all source functions."""
        print("🔍 Discovering source functions...")
        self.discovered_sources = discover_source_functions()

        if not self.discovered_sources:
            print("❌ No source functions were discovered")
            return False

        print(f"✓ Found {len(self.discovered_sources)} source functions:")
        for source_name, _ in self.discovered_sources:
            print(f"  - {source_name}")

        return True

    def test_get_candles_basic_functionality(self, source_name: str, source_function: Callable) -> TestResult:
        """
        Test 1: Run get_candles_from_{source} with configurable parameters.

        Uses TEST_CONFIG parameters and adapts validation rules based on timeframe.
        """
        # Get test parameters from configuration
        currency_pair = TEST_CONFIG['currency_pair']
        timeframe = TEST_CONFIG['timeframe']
        candles = TEST_CONFIG['candles']
        ecc = TEST_CONFIG['ecc']

        # Get timeframe rules for display
        rules = TIMEFRAME_RULES.get(timeframe, {})
        max_age_minutes = rules.get('max_age_minutes', 'unknown')

        print(f"\n🧪 Testing {source_name} with parameters:")
        print(f"    Currency Pair: {currency_pair}")
        print(f"    Timeframe: {timeframe}")
        print(f"    Candles: {candles}")
        print(f"    ECC: {ecc}")
        if max_age_minutes != 'unknown':
            if max_age_minutes >= 60:
                print(f"    Max Data Age: {max_age_minutes / 60:.1f} hours")
            else:
                print(f"    Max Data Age: {max_age_minutes} minutes")

        try:
            # Call the source function
            result = source_function(currency_pair, timeframe, candles, ecc)

            # Validate the response and get sample data
            is_valid, errors, sample_data = validate_dataframe_response(result, source_name, timeframe, candles)

            if not is_valid:
                error_msg = f"Validation failed for {source_name}:\n" + "\n".join(f"  - {error}" for error in errors)
                print(f"❌ {source_name}: FAILED")
                for error in errors:
                    print(f"    {error}")
                return TestResult("basic_functionality", source_name, False, error_msg, sample_data = sample_data)

            # Additional success logging
            details = {
                    'currency_pair'    : currency_pair,
                    'timeframe'        : timeframe,
                    'candles_requested': candles,
                    'ecc'              : ecc
            }

            if not result.empty:
                newest_ts = result['timestamp'].max()
                oldest_ts = result['timestamp'].min()
                current_time = datetime.now().timestamp()
                age_minutes = (current_time - newest_ts) / 60

                details.update({
                        'candles_returned'  : len(result),
                        'data_age_minutes'  : round(age_minutes, 1),
                        'time_range_minutes': round((newest_ts - oldest_ts) / 60, 1)
                })

                print(f"✓ {source_name}: Successfully returned {len(result)} candles")
                if age_minutes >= 60:
                    print(f"  - Data age: {age_minutes / 60:.1f} hours")
                else:
                    print(f"  - Data age: {age_minutes:.1f} minutes")
                print(f"  - Time range: {(newest_ts - oldest_ts) / 60:.1f} minutes")

            return TestResult("basic_functionality", source_name, True, "All validations passed", details, sample_data)

        except Exception as e:
            error_msg = f"Exception in {source_name}: {str(e)}"
            print(f"❌ {source_name}: EXCEPTION - {str(e)}")
            # Still try to get sample data even on exception
            sample_data = get_dataframe_sample(None, candles)
            return TestResult("basic_functionality", source_name, False, error_msg, sample_data = sample_data)

    def run_all_tests(self) -> bool:
        """Run all tests on all discovered sources."""
        if not self.discover_sources():
            return False

        print(f"\n🚀 Running tests on {len(self.discovered_sources)} sources...")
        print("=" * 60)

        all_passed = True

        for source_name, source_function in self.discovered_sources:
            # Test 1: Basic functionality
            result = self.test_get_candles_basic_functionality(source_name, source_function)
            self.results.append(result)

            if not result.passed:
                all_passed = False

        return all_passed

    def print_summary(self):
        """Print a summary of all test results."""
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)

        passed_count = sum(1 for r in self.results if r.passed)
        total_count = len(self.results)

        print(f"Total tests: {total_count}")
        print(f"Passed: {passed_count}")
        print(f"Failed: {total_count - passed_count}")

        if passed_count == total_count:
            print("🎉 ALL TESTS PASSED!")
        else:
            print("❌ Some tests failed")

        print("\nDetailed Results:")
        print("-" * 80)

        for result in self.results:
            status = "✓ PASS" if result.passed else "❌ FAIL"
            print(f"\n{status} | {result.source_name.upper()} | {result.test_name}")

            # Print test details
            if result.details:
                print("  📈 Test Metrics:")
                for key, value in result.details.items():
                    print(f"      {key}: {value}")

            # Print sample data
            if result.sample_data:
                sample = result.sample_data
                if 'total_rows' in sample:
                    sample_rows = sample.get('sample_rows', 0)
                    print(f"  📊 Data Sample (Total rows: {sample['total_rows']}, Showing: {sample_rows}):")
                    if 'columns' in sample:
                        print(f"      Columns: {sample['columns']}")

                if 'timestamps_sample' in sample and sample['timestamps_sample'] != "No data":
                    print("      📋 Timestamps (newest first):")
                    for line in sample['timestamps_sample'].split('\n'):
                        if line.strip():
                            print(f"        {line}")

            # Print error details
            if not result.passed:
                print("  ❌ Error Details:")
                for line in result.message.split('\n'):
                    if line.strip():
                        print(f"      {line}")

        print("\n" + "-" * 80)


def run_tests():
    """Main function to run all tests."""
    print("🧪 Source Function Test Suite")
    print("=" * 60)
    print("Testing all get_candles_from_{source} functions")
    print("=" * 60)

    # Display current test configuration
    print("📋 Current Test Configuration:")
    print(f"   Currency Pair: {TEST_CONFIG['currency_pair']}")
    print(f"   Timeframe: {TEST_CONFIG['timeframe']}")
    print(f"   Candles: {TEST_CONFIG['candles']}")
    print(f"   ECC: {TEST_CONFIG['ecc']}")

    # Show timeframe-specific rules
    if TEST_CONFIG['timeframe'] in TIMEFRAME_RULES:
        rules = TIMEFRAME_RULES[TEST_CONFIG['timeframe']]
        max_age = rules['max_age_minutes']
        if max_age >= 60:
            print(f"   Max Data Age: {max_age / 60:.1f} hours")
        else:
            print(f"   Max Data Age: {max_age} minutes")

    print("=" * 60)

    # Create and run test runner
    runner = SourceTestRunner()
    all_passed = runner.run_all_tests()

    # Print summary
    runner.print_summary()

    # Return exit code
    return 0 if all_passed else 1


if __name__ == "__main__":
    # Run the test suite
    exit_code = run_tests()
    sys.exit(exit_code)
