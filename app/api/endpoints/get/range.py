from typing import Optional

import pandas as pd
from fastapi import APIRouter

from app.core.refresh_source_db_by_range import refresh_db_by_range
from app.core.helpers.convert_date_range_to_candles import convert_date_range_to_candles
from app.core.helpers.convert_date_to_utc_timestamp import convert_date_to_utc_timestamp
from app.api.endpoints.get.helpers.get_missing_timestamps import get_missing_timestamps
from app.api.endpoints.get.helpers.is_data_complete import is_data_complete
from app.db.sqlite.get_from_db_by_timestamp import get_from_db_by_timestamp
from app.logger.get_logger import logger, log
from app.config import EXCLUDE_CURRENT_CANDLE_BITSTAMP

router = APIRouter()


@router.get("/{currency_pair}/{timeframe}/{from_date}/{to_date}")
@log
def get_range_endpoint(currency_pair: str, timeframe: str, from_date: str, to_date: str):
    ecc = EXCLUDE_CURRENT_CANDLE_BITSTAMP
    from_timestamp = convert_date_to_utc_timestamp(from_date)
    if to_date == "now":
        to_timestamp = None
    else:
        to_timestamp = convert_date_to_utc_timestamp(to_date)

    candles = convert_date_range_to_candles(from_date, to_date, timeframe)

    # First attempt from DB
    data, errors1 = get_validated_data(currency_pair, timeframe, from_timestamp, to_timestamp, candles, ecc)
    if data:
        return data

    # Refresh data and try again
    refresh_db_by_range(currency_pair, timeframe, from_date, to_date, candles, ecc)

    data, errors2 = get_validated_data(currency_pair, timeframe, from_timestamp, to_timestamp, candles, ecc)
    if data:
        return data

    logger.error("------------------------------------------------------------")
    return {"error"  : "dabot.ohlc Failed", "errors1": errors1, "errors2": errors2}


def get_validated_data(currency_pair: str, timeframe: str, from_timestamp: int,
                       to_timestamp: int, candles: int, ecc: bool) -> tuple[Optional[dict], Optional[list[str]]]:
    try:
        internal_data = get_from_db_by_timestamp(currency_pair, timeframe, from_timestamp, to_timestamp, "bitstamp")
    except (ValueError, Exception) as e:
        return None, [str(e)]

    is_valid, errors = _is_range_data_valid(internal_data, timeframe, from_timestamp, to_timestamp, candles, ecc)
    if is_valid:
        return internal_data.sort_values(by = 'timestamp', ascending = False).to_dict(orient = 'records'), None
    return None, errors


@log
def _is_range_data_valid(df, timeframe: str, from_timestamp: int, to_timestamp: int, candles: int, ecc: bool) \
        -> tuple[bool, list[str]]:
    errors = []

    if df is None:
        errors.append("DataFrame is None")
        logger.warn("Validation failed: {}".format(errors))
        return False, errors
    if not isinstance(df, pd.DataFrame):
        errors.append("Input is not a pandas DataFrame")
        return False, errors
    if df.empty:
        errors.append("DataFrame is empty")
    is_complete = is_data_complete(df, candles - 1 if ecc else candles)
    if not is_complete:
        errors.append("Wrong number of candles")
    missing_timestamps = get_missing_timestamps(df, timeframe, from_timestamp, to_timestamp)
    if missing_timestamps:
        errors.append(f"Missing timestamps: {missing_timestamps}")
    if errors:
        logger.warn("Validation failed: {}".format(errors))
        return False, errors
    return True, []
