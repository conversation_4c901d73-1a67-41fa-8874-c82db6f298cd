"""
Kraken Candles Data Fetcher

This module fetches OHLC candle data from the Kraken API following the same pattern
as other source handlers in the project.

Kraken API Details:
- Endpoint: GET /0/public/OHLC
- Rate Limits: Varies by endpoint and tier
- Data Format: Array of arrays with OHLC data
- Timeframes: 1, 5, 15, 30, 60, 240, 1440, 10080, 21600 (minutes)
"""

import json
import pandas as pd
from typing import Optional
from datetime import datetime

from ratelimit import limits

from app.sources.kraken.api.spot.market_data.get_ohlc_data import request
from app.logger.get_logger import log, logger

# Kraken timeframe mapping (minutes)
KRAKEN_TIMEFRAMES = {
        'm1' : 1,
        'm5' : 5,
        'm15': 15,
        'm30': 30,
        'h1' : 60,
        'h4' : 240,
        'd1' : 1440,
        'w1' : 10080,
        'M1' : 21600
}

def _convert_currency_pair_format(currency_pair: str) -> str:
    """
    Convert currency pair from internal format (btc-usd) to Kraken format (BTC/USD).

    Args:
        currency_pair: Internal pair format (e.g., 'btc-usd', 'eth-btc')

    Returns:
        str: Kraken API format (e.g., 'BTC/USD', 'ETH/BTC')
    """
    if '-' not in currency_pair:
        raise ValueError(f"Invalid currency pair format: {currency_pair}. Expected format: 'base-quote'")

    base, quote = currency_pair.upper().split('-')
    return f"{base}/{quote}"


@log
@limits(calls=60, period=60)
def get_candles_from_kraken(currency_pair: str, timeframe: str, candles: int, ecc: bool = True) -> Optional[
    pd.DataFrame]:
    """
    Fetch OHLC candle data from Kraken API.

    Note: Kraken API has a hard limit of 720 historical candles per request.
    Requests for more than 720 candles will be automatically limited to 720.

    Args:
        currency_pair: Trading pair (e.g., 'btc-usd')
        timeframe: Time interval (e.g., 'm1', 'h1', 'd1')
        candles: Number of candles to fetch (max 720 due to Kraken limitation)
        ecc: Exclude current candle (default: True)

    Returns:
        DataFrame with OHLC data or None if error
    """
    try:
        # Validate currency pair format by attempting conversion
        try:
            _convert_currency_pair_format(currency_pair)
        except ValueError as e:
            logger.error(f"Invalid currency pair format: {str(e)}")
            return None

        # Validate inputs
        if timeframe not in KRAKEN_TIMEFRAMES:
            logger.error(f"Unsupported timeframe for Kraken: {timeframe}")
            return None

        # Verify currency pair exists on Kraken
        if not verify_kraken_pair_exists(currency_pair):
            logger.error(f"Currency pair verification failed for Kraken: {currency_pair}")
            return None

        # Kraken limits to 720 historic candles
        if candles > 720:
            logger.warning(f"Kraken limits to 720 candles, requested {candles}. Using 720.")
            candles = 720

        # Prepare API request
        kraken_pair = _convert_currency_pair_format(currency_pair)
        kraken_interval = KRAKEN_TIMEFRAMES[timeframe]

        logger.info(f"Fetching {candles} {timeframe} candles for {currency_pair} from Kraken")

        # Make API request
        response = request(
                method = "GET",
                path = "/0/public/OHLC",
                query = {
                        "pair"    : kraken_pair,
                        "interval": kraken_interval
                },
                environment = "https://api.kraken.com"
        )

        # Parse response
        response_data = response.read().decode()
        data = json.loads(response_data)

        # Check for errors
        if 'error' in data and data['error']:
            logger.error(f"Kraken API error: {data['error']}")
            return None

        if 'result' not in data:
            logger.error("No result in Kraken API response")
            return None

        # Extract OHLC data
        result = data['result']

        # Find the pair data (Kraken returns pair name as key)
        ohlc_data = None
        for key, value in result.items():
            if key != 'last' and isinstance(value, list):
                ohlc_data = value
                break

        if not ohlc_data:
            logger.error("No OHLC data found in Kraken response")
            return None

        # Convert to DataFrame
        df = _parse_kraken_ohlc_data(ohlc_data, ecc)

        # Limit to requested number of candles
        if len(df) > candles:
            df = df.tail(candles)

        logger.info(f"Successfully fetched {len(df)} candles from Kraken")
        return df

    except Exception as e:
        logger.error(f"Error fetching data from Kraken: {str(e)}")
        return None


def _parse_kraken_ohlc_data(ohlc_data: list, ecc: bool = True) -> pd.DataFrame:
    """
    Parse Kraken OHLC array data into DataFrame.

    Kraken OHLC format: [time, open, high, low, close, vwap, volume, count]

    Args:
        ohlc_data: List of OHLC arrays from Kraken
        ecc: Exclude current candle

    Returns:
        DataFrame with standardized OHLC format
    """
    try:
        # Convert to DataFrame
        columns = ['timestamp', 'open', 'high', 'low', 'close', 'vwap', 'volume', 'count']
        df = pd.DataFrame(ohlc_data, columns = columns)

        # Exclude current candle if requested (last entry is current/incomplete)
        if ecc and len(df) > 0:
            df = df[:-1]

        # Convert data types
        df['timestamp'] = pd.to_numeric(df['timestamp'], errors = 'coerce').astype('int64')
        df['open'] = pd.to_numeric(df['open'], errors = 'coerce').astype('float64')
        df['high'] = pd.to_numeric(df['high'], errors = 'coerce').astype('float64')
        df['low'] = pd.to_numeric(df['low'], errors = 'coerce').astype('float64')
        df['close'] = pd.to_numeric(df['close'], errors = 'coerce').astype('float64')
        df['volume'] = pd.to_numeric(df['volume'], errors = 'coerce').astype('float64')

        # Create datetime column
        df['date'] = pd.to_datetime(df['timestamp'], unit = 's', utc = True)

        # Select only required columns (matching other sources)
        df = df[['timestamp', 'date', 'open', 'high', 'low', 'close', 'volume']]

        # Remove any rows with NaN values
        df = df.dropna()

        # Sort by timestamp (ascending)
        df = df.sort_values('timestamp').reset_index(drop = True)

        return df

    except Exception as e:
        logger.error(f"Error parsing Kraken OHLC data: {str(e)}")
        return pd.DataFrame()


def verify_kraken_pair_exists(currency_pair: str) -> bool:
    """
    Verify if a currency pair exists on Kraken by testing with a minimal API call.

    Args:
        currency_pair: Internal pair name (e.g., 'btc-usd', 'eth-btc')

    Returns:
        bool: True if pair exists and is tradable
    """
    try:
        # Convert currency pair to Kraken format
        kraken_pair = _convert_currency_pair_format(currency_pair)

        # Test with minimal OHLC request (1 candle)
        response = request(
                method = "GET",
                path = "/0/public/OHLC",
                query = {
                        "pair"    : kraken_pair,
                        "interval": 60  # 1 hour
                },
                environment = "https://api.kraken.com"
        )

        data = json.loads(response.read().decode())

        if 'error' in data and data['error']:
            logger.error(f"Kraken API error for {currency_pair} ({kraken_pair}): {data['error']}")
            logger.error(f"Currency pair {currency_pair} does not exist on Kraken")
            return False

        if 'result' in data and data['result']:
            # Check if we got actual pair data (not just 'last' key)
            pair_keys = [k for k in data['result'].keys() if k != 'last']
            if pair_keys:
                logger.info(f"Verified pair {currency_pair} -> {kraken_pair} -> {pair_keys[0]}")
                return True

        logger.error(f"No valid data returned for {currency_pair} ({kraken_pair})")
        logger.error(f"Currency pair {currency_pair} does not exist on Kraken")
        return False

    except Exception as e:
        logger.error(f"Error verifying pair {currency_pair}: {str(e)}")
        return False


def test_kraken_connection() -> bool:
    """Test connection to Kraken API."""
    try:
        response = request(
                method = "GET",
                path = "/0/public/Time",
                environment = "https://api.kraken.com"
        )
        data = json.loads(response.read().decode())
        return 'error' not in data or not data['error']
    except Exception as e:
        logger.error(f"Kraken connection test failed: {str(e)}")
        return False


if __name__ == "__main__":
    # Test the function with multiple pairs
    print("Testing Kraken API connection...")
    if test_kraken_connection():
        print("✓ Kraken API connection successful")

        # Test multiple currency pairs
        test_pairs = ['btc-usd', 'eth-usd', 'eth-btc']

        for pair in test_pairs:
            print(f"\nTesting {pair.upper()}...")
            data = get_candles_from_kraken(pair, 'h1', 5)
            if data is not None and not data.empty:
                print(f"✓ Successfully fetched {len(data)} candles")
                print(f"  Price range: ${data['low'].min():.2f} - ${data['high'].max():.2f}")
            else:
                print(f"✗ Failed to fetch data for {pair}")
    else:
        print("✗ Kraken API connection failed")
