from importlib import import_module
from app.db.sqlite.store_in_db import store_in_db
from app.config import SOURCES
from app.logger.get_logger import log, logger


@log
def refresh_db_by_candles(currency_pair: str, timeframe: str, candles: int, ecc: bool = True) -> None:
    """
    Fetches candle data from multiple external sources and stores it in the database.
    
    Args:
        currency_pair: Trading pair (e.g., 'btc-usd')
        timeframe: Time interval (e.g., 'm1', 'h1', 'd1')
        candles: Number of candles to fetch
        ecc: Exclude current candle (default: True)
    """
    for source in SOURCES:
        try:
            # Dynamically import the source handler
            module_path = f"app.sources.{source}.get_candles_from_{source}"
            module = import_module(module_path)
            handler = getattr(module, f"get_candles_from_{source}")

            # Call the handler and store data
            source_data = handler(currency_pair, timeframe, candles, ecc)
            if source_data is not None:
                store_in_db(currency_pair, timeframe, source_data, source)
        except ImportError as e:
            logger.error(f"Failed to import handler for source {source}: {str(e)}")
        except Exception as e:
            logger.error(f"Failed to fetch data from {source}: {str(e)}")

if __name__ == "__main__":
    refresh_db_by_candles("btc-usd", "h1", 100)
