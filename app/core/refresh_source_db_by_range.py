from importlib import import_module
from app.db.sqlite.store_in_db import store_in_db
from app.config import SOURCES
from app.logger.get_logger import log, logger


@log
def refresh_db_by_range(currency_pair: str, timeframe: str, from_date: str, to_date: str, candles: int, ecc: bool) -> None:
    for source in SOURCES:
        try:
            # Dynamically import the source handler
            module_path = f"app.sources.{source}.get_range_from_{source}"
            module = import_module(module_path)
            handler = getattr(module, f"get_range_from_{source}")

            # Call the handler and store data
            source_data = handler(currency_pair, timeframe, from_date, to_date, candles, ecc)
            if source_data is not None:
                store_in_db(currency_pair, timeframe, source_data, source)
        except ImportError as e:
            logger.error(f"Failed to import handler for source {source}: {str(e)}")
        except Exception as e:
            logger.error(f"Failed to fetch data from {source}: {str(e)}")
